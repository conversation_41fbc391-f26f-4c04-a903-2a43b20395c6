#!/usr/bin/env node

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from "@modelcontextprotocol/sdk/types.js";

// 内存存储 TODO 数据
let todos = [
  { id: 1, title: "学习 MCP", completed: false, createdAt: new Date().toISOString() },
  { id: 2, title: "写代码示例", completed: true, createdAt: new Date().toISOString() }
];
let nextId = 3;

// 创建 MCP 服务器
const server = new Server(
  {
    name: "todo-server",
    version: "1.0.0",
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

// 列出可用工具
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: "list_todos",
        description: "获取所有 TODO 项目",
        inputSchema: {
          type: "object",
          properties: {},
        },
      },
      {
        name: "add_todo",
        description: "添加新的 TODO 项目",
        inputSchema: {
          type: "object",
          properties: {
            title: {
              type: "string",
              description: "TODO 项目标题",
            },
          },
          required: ["title"],
        },
      },
      {
        name: "update_todo",
        description: "更新 TODO 项目",
        inputSchema: {
          type: "object",
          properties: {
            id: {
              type: "number",
              description: "TODO 项目 ID",
            },
            title: {
              type: "string",
              description: "新的标题（可选）",
            },
            completed: {
              type: "boolean",
              description: "完成状态（可选）",
            },
          },
          required: ["id"],
        },
      },
      {
        name: "delete_todo",
        description: "删除 TODO 项目",
        inputSchema: {
          type: "object",
          properties: {
            id: {
              type: "number",
              description: "要删除的 TODO 项目 ID",
            },
          },
          required: ["id"],
        },
      },
    ],
  };
});

// 处理工具调用
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;

  switch (name) {
    case "list_todos":
      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(todos, null, 2),
          },
        ],
      };

    case "add_todo":
      const newTodo = {
        id: nextId++,
        title: args.title,
        completed: false,
        createdAt: new Date().toISOString(),
      };
      todos.push(newTodo);
      return {
        content: [
          {
            type: "text",
            text: `已添加 TODO: ${JSON.stringify(newTodo, null, 2)}`,
          },
        ],
      };

    case "update_todo":
      const todoIndex = todos.findIndex(t => t.id === args.id);
      if (todoIndex === -1) {
        return {
          content: [
            {
              type: "text",
              text: `未找到 ID 为 ${args.id} 的 TODO 项目`,
            },
          ],
        };
      }
      
      if (args.title !== undefined) {
        todos[todoIndex].title = args.title;
      }
      if (args.completed !== undefined) {
        todos[todoIndex].completed = args.completed;
      }
      todos[todoIndex].updatedAt = new Date().toISOString();
      
      return {
        content: [
          {
            type: "text",
            text: `已更新 TODO: ${JSON.stringify(todos[todoIndex], null, 2)}`,
          },
        ],
      };

    case "delete_todo":
      const deleteIndex = todos.findIndex(t => t.id === args.id);
      if (deleteIndex === -1) {
        return {
          content: [
            {
              type: "text",
              text: `未找到 ID 为 ${args.id} 的 TODO 项目`,
            },
          ],
        };
      }
      
      const deletedTodo = todos.splice(deleteIndex, 1)[0];
      return {
        content: [
          {
            type: "text",
            text: `已删除 TODO: ${JSON.stringify(deletedTodo, null, 2)}`,
          },
        ],
      };

    default:
      throw new Error(`未知工具: ${name}`);
  }
});

// 启动服务器
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error("MCP TODO 服务器已启动");
}

main().catch((error) => {
  console.error("服务器启动失败:", error);
  process.exit(1);
});
