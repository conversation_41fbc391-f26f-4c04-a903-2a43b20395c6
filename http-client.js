#!/usr/bin/env node

import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
import readline from "readline";

// 模拟 OpenAI 格式的聊天完成
class MCPChatClient {
  constructor() {
    this.client = new Client(
      {
        name: "mcp-chat-client",
        version: "1.0.0",
      },
      {
        capabilities: {},
      }
    );
    this.connected = false;
  }

  async connect () {
    if (this.connected) return;

    const transport = new StdioClientTransport({
      command: "node",
      args: ["server.js"],
    });

    await this.client.connect(transport);
    this.connected = true;
    console.log("✅ 已连接到 MCP TODO 服务器");
  }

  async listTools () {
    await this.connect();
    const response = await this.client.listTools();
    return response.tools;
  }

  async callTool (name, args = {}) {
    await this.connect();
    const response = await this.client.callTool({
      name,
      arguments: args,
    });
    return response.content[0].text;
  }

  // 模拟 OpenAI 聊天完成 API
  async chatCompletion (messages, tools = null) {
    await this.connect();

    const lastMessage = messages[messages.length - 1];
    const userInput = lastMessage.content;

    // 简单的意图识别
    let toolCall = null;
    let response = "";

    if (userInput.includes("列出") || userInput.includes("查看") || userInput.includes("显示")) {
      toolCall = { name: "list_todos", arguments: {} };
    } else if (userInput.includes("添加") || userInput.includes("新增")) {
      const titleMatch = userInput.match(/(?:添加|新增)\s*(.+)/);
      if (titleMatch && titleMatch[1]) {
        toolCall = { name: "add_todo", arguments: { title: titleMatch[1].trim() } };
      }
    } else if (userInput.includes("完成") || userInput.includes("标记完成")) {
      const idMatch = userInput.match(/(\d+)/);
      if (idMatch) {
        toolCall = { name: "update_todo", arguments: { id: parseInt(idMatch[1]), completed: true } };
      }
    } else if (userInput.includes("删除")) {
      const idMatch = userInput.match(/删除\s*(\d+)/);
      if (idMatch) {
        toolCall = { name: "delete_todo", arguments: { id: parseInt(idMatch[1]) } };
      }
    }

    if (toolCall) {
      try {
        const result = await this.callTool(toolCall.name, toolCall.arguments);
        response = `执行了工具 ${toolCall.name}，结果：\n${result}`;
      } catch (error) {
        response = `执行工具时出错: ${error.message}`;
      }
    } else {
      response = "抱歉，我没有理解您的请求。您可以尝试：\n- 列出所有 TODO\n- 添加新的 TODO\n- 完成某个 TODO（通过 ID）\n- 删除某个 TODO（通过 ID）";
    }

    // 返回 OpenAI 格式的响应
    return {
      id: `chatcmpl-${Date.now()}`,
      object: "chat.completion",
      created: Math.floor(Date.now() / 1000),
      model: "mcp-todo-model",
      choices: [
        {
          index: 0,
          message: {
            role: "assistant",
            content: response,
          },
          finish_reason: "stop",
        },
      ],
      usage: {
        prompt_tokens: userInput.length,
        completion_tokens: response.length,
        total_tokens: userInput.length + response.length,
      },
    };
  }

  async interactive () {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    console.log("\n=== MCP TODO 聊天客户端 (OpenAI 格式) ===");
    console.log("您可以用自然语言与 TODO 系统交互，例如：");
    console.log("- '列出所有 TODO'");
    console.log("- '添加学习 JavaScript'");
    console.log("- '完成 1'");
    console.log("- '删除 2'");
    console.log("输入 'quit' 退出\n");

    const messages = [];

    const askQuestion = () => {
      rl.question("您: ", async (input) => {
        if (input.trim().toLowerCase() === "quit") {
          console.log("再见!");
          rl.close();
          process.exit(0);
        }

        messages.push({ role: "user", content: input });

        try {
          const response = await this.chatCompletion(messages);
          const assistantMessage = response.choices[0].message.content;

          console.log(`\n助手: ${assistantMessage}\n`);
          messages.push({ role: "assistant", content: assistantMessage });
        } catch (error) {
          console.error("错误:", error.message);
        }

        askQuestion();
      });
    };

    askQuestion();
  }
}

// 启动聊天客户端
async function main () {
  const client = new MCPChatClient();
  await client.interactive();
}

main().catch((error) => {
  console.error("聊天客户端启动失败:", error);
  process.exit(1);
});
