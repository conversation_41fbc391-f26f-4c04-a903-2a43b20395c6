# MCP TODO 示例

这是一个最小化的 MCP (Model Context Protocol) TODO 应用示例，演示如何创建和使用 MCP 服务器。

## 功能特性

- ✅ 列出所有 TODO 项目
- ✅ 添加新的 TODO 项目
- ✅ 更新 TODO 项目（标题和完成状态）
- ✅ 删除 TODO 项目
- ✅ 数据存储在内存中（JS 变量）

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 启动交互式客户端

```bash
npm run client
```

### 3. 使用命令

在交互式界面中，你可以使用以下命令：

- `list` - 列出所有 TODO
- `add <标题>` - 添加新 TODO
- `update <id> [title=新标题] [completed=true/false]` - 更新 TODO
- `delete <id>` - 删除 TODO
- `tools` - 显示可用工具
- `quit` - 退出

### 示例操作

```
请输入命令: list
当前 TODO 列表:
[
  {
    "id": 1,
    "title": "学习 MCP",
    "completed": false,
    "createdAt": "2025-01-08T..."
  },
  {
    "id": 2,
    "title": "写代码示例",
    "completed": true,
    "createdAt": "2025-01-08T..."
  }
]

请输入命令: add 完成项目文档
已添加 TODO: {
  "id": 3,
  "title": "完成项目文档",
  "completed": false,
  "createdAt": "2025-01-08T..."
}

请输入命令: update 1 completed=true
已更新 TODO: {
  "id": 1,
  "title": "学习 MCP",
  "completed": true,
  "createdAt": "2025-01-08T...",
  "updatedAt": "2025-01-08T..."
}
```

## 文件结构

- `server.js` - MCP 服务器实现，提供 TODO 的增删改查功能
- `client.js` - 交互式客户端，演示如何使用 MCP 服务器
- `package.json` - 项目配置和依赖

## MCP 工具

服务器提供以下 MCP 工具：

1. **list_todos** - 获取所有 TODO 项目
2. **add_todo** - 添加新的 TODO 项目
3. **update_todo** - 更新 TODO 项目
4. **delete_todo** - 删除 TODO 项目

## 技术栈

- Node.js
- MCP SDK (@modelcontextprotocol/sdk)
- ES6 模块
- 内存数据存储
